# 组件文档

## 组件列表

### 图表组件

- [YcCharts](./components/YcCharts.md) - 基于 ECharts 的响应式图表组件
  - 支持多种图表类型：柱状图、折线图、饼图、面积图等
  - 完整的移动端适配和响应式设计
  - 智能图例管理和自动布局调整

## 快速开始

### 安装依赖

```bash
# 安装 ECharts 相关依赖
npm install vue-echarts echarts

# 如果使用 VueUse (推荐)
npm install @vueuse/core
```

### 全局注册

```javascript
// main.js
import { createApp } from 'vue'
import YcCharts from '@/components/YcCharts/index.vue'

const app = createApp(App)
app.component('YcCharts', YcCharts)
```

### 按需引入

```vue
<script setup>
import YcCharts from '@/components/YcCharts/index.vue'
</script>
```

## 开发规范

### 组件命名

- 组件名使用 PascalCase
- 文件名使用 kebab-case
- Props 使用 camelCase

### 文档结构

每个组件文档应包含：

1. **概述** - 组件的基本介绍
2. **特性** - 主要功能特点
3. **安装依赖** - 所需的外部依赖
4. **基础用法** - 最简单的使用示例
5. **Props 属性** - 详细的属性说明
6. **插槽** - 可用的插槽
7. **示例集合** - 各种使用场景的示例
8. **API 参考** - 方法和事件
9. **注意事项** - 使用时的注意点
10. **常见问题** - FAQ

## 贡献指南

### 添加新组件文档

1. 在 `docs/components/` 目录下创建 Markdown 文件
2. 按照上述文档结构编写
3. 在本文件中添加链接
4. 提供完整的代码示例

### 文档规范

- 使用中文编写
- 代码示例要完整可运行
- 包含必要的类型说明
- 提供多种使用场景的示例

## 更新日志

- **2024-01-01**: 初始化文档结构
- **2024-01-02**: 添加 YcCharts 组件文档
- **2024-01-03**: 完善移动端响应式文档，新增多种图表类型示例
