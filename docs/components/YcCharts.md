# YcCharts 组件文档

## 概述

`YcCharts` 是一个基于 Vue 3 和 ECharts 的高度可定制的图表组件，提供了丰富的配置选项和响应式设计支持。

## 特性

- 🎨 **高度可定制**：支持完全自定义 ECharts 配置
- 📱 **响应式设计**：自动适配不同屏幕尺寸，支持桌面、平板、手机
- 🔄 **自动更新**：配置变化时自动重新渲染
- 🎯 **智能图例**：自动生成和管理图例
- 💾 **内存安全**：自动清理事件监听器
- 🎭 **插槽支持**：支持自定义标题内容
- 📐 **智能布局**：根据设备类型自动调整图表布局和样式
- 🖱️ **触摸友好**：移动端优化的交互体验

## 安装依赖

```bash
npm install vue-echarts echarts
```

## 基础用法

```vue
<template>
  <YcCharts
    title="销量统计"
    :options="chartOptions"
    :loading="loading"
    height="400px"
  />
</template>

<script setup>
import YcCharts from '@/components/YcCharts/index.vue'

const loading = ref(false)
const chartOptions = ref({
  xAxis: {
    type: 'category',
    data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
  },
  yAxis: {
    type: 'value'
  },
  series: [{
    name: '销量',
    data: [120, 200, 150, 80, 70, 110, 130],
    type: 'bar'
  }]
})
</script>
```

## Props 属性

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| `title` | `String` | `''` | ❌ | 图表标题文本 |
| `titleIcon` | `String` | `'data1'` | ❌ | 标题左侧图标名称 |
| `options` | `Object` | - | ✅ | ECharts 配置对象 |
| `overrideOptions` | `Boolean` | `false` | ❌ | 是否完全覆盖基础配置 |
| `loading` | `Boolean` | `false` | ❌ | 是否显示加载动画 |
| `height` | `String \| Number` | `'50vh'` | ❌ | 图表容器高度 |

## 插槽

### title 插槽

自定义标题内容，替换默认的 `BlockTitle` 组件。

```vue
<template>
  <YcCharts :options="chartOptions">
    <template #title>
      <div class="custom-title">
        <i class="icon-chart"></i>
        <span>自定义标题</span>
      </div>
    </template>
  </YcCharts>
</template>
```

## 配置模式

### 1. 合并模式（默认）

组件会将传入的 `options` 与内置的基础配置进行深度合并：

```vue
<template>
  <YcCharts :options="chartOptions" />
</template>

<script setup>
const chartOptions = {
  // 只需要配置特定的选项，其他会使用默认配置
  series: [{
    name: '数据',
    type: 'line',
    data: [1, 2, 3, 4, 5]
  }]
}
</script>
```

### 2. 覆盖模式

设置 `overrideOptions: true` 完全使用自定义配置：

```vue
<template>
  <YcCharts 
    :options="fullChartOptions" 
    :override-options="true" 
  />
</template>

<script setup>
const fullChartOptions = {
  // 需要提供完整的 ECharts 配置
  title: { text: '完全自定义' },
  xAxis: { type: 'category', data: ['A', 'B', 'C'] },
  yAxis: { type: 'value' },
  series: [{ type: 'bar', data: [1, 2, 3] }]
}
</script>
```

## 内置基础配置

组件提供了以下默认配置：

```javascript
{
  // 颜色主题
  color: [...], // 来自 config.js
  
  // 提示框配置
  tooltip: {
    confine: true,
    formatter: TooltipFormatter // 自定义格式化器
  },
  
  // 网格配置
  grid: {
    left: '5%',
    right: '14%',
    top: '8%',
    bottom: '8%',
    containLabel: true
  },
  
  // 坐标轴配置
  xAxis: {
    type: 'category',
    axisTick: { show: true, alignWithLabel: true }
  },
  yAxis: {
    type: 'category', 
    axisTick: { show: true, alignWithLabel: true }
  },
  
  // 图例配置
  legend: {
    right: 'right',
    top: 'middle',
    type: 'scroll',
    orient: 'vertical'
  }
}
```

## 响应式特性

### 多设备自动适配

组件内置了完整的响应式系统，支持桌面、平板、手机等多种设备：

#### 断点系统

| 设备类型 | 屏幕宽度 | 优化特性 |
|---------|---------|---------|
| 桌面 | >1024px | 完整功能，垂直图例 |
| 平板 | 769-1024px | 水平图例，调整网格 |
| 手机 | 481-768px | 压缩布局，优化字体 |
| 小屏手机 | ≤480px | 旋转标签，最小化间距 |

#### 自动配置调整

```javascript
// 手机设备配置（≤768px）
{
  query: { maxWidth: 768 },
  option: {
    grid: {
      left: '5%', right: '5%',
      top: '15%', bottom: '15%'
    },
    legend: {
      orient: 'horizontal',
      top: 'top',
      itemWidth: 12,
      itemHeight: 8,
      textStyle: { fontSize: 10 }
    },
    tooltip: {
      extraCssText: 'max-width: 80vw; font-size: 12px;'
    },
    xAxis: {
      axisLabel: { fontSize: 10 }
    },
    yAxis: {
      axisLabel: { fontSize: 10 }
    }
  }
}

// 小屏手机配置（≤480px）
{
  query: { maxWidth: 480 },
  option: {
    xAxis: {
      axisLabel: {
        fontSize: 9,
        rotate: 45  // 旋转标签节省空间
      }
    }
  }
}
```

### 智能图例管理

组件会自动从 `series` 配置中提取系列名称，生成图例数据：

```vue
<script setup>
const chartOptions = {
  series: [
    { name: '销量', type: 'bar', data: [1, 2, 3] },
    { name: '利润', type: 'line', data: [4, 5, 6] }
  ]
}
// 自动生成 legend.data = ['销量', '利润']
</script>
```

## 高级用法

### 响应式高度设置

```vue
<template>
  <YcCharts
    :options="chartOptions"
    :height="chartHeight"
    :loading="loading"
  />
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const windowWidth = ref(window.innerWidth)

// 响应式图表高度
const chartHeight = computed(() => {
  if (windowWidth.value <= 480) return '250px'  // 小屏手机
  if (windowWidth.value <= 768) return '300px'  // 手机
  if (windowWidth.value <= 1024) return '350px' // 平板
  return '400px'  // 桌面
})

// 窗口大小监听
const handleResize = () => {
  windowWidth.value = window.innerWidth
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>
```

### 动态数据更新

```vue
<template>
  <YcCharts :options="chartOptions" :loading="loading" />
  <button @click="updateData">更新数据</button>
</template>

<script setup>
const loading = ref(false)
const chartOptions = ref({
  series: [{ type: 'bar', data: [1, 2, 3] }]
})

const updateData = async () => {
  loading.value = true
  try {
    const newData = await fetchData()
    chartOptions.value.series[0].data = newData
  } finally {
    loading.value = false
  }
}
</script>
```

### 自定义样式

```vue
<template>
  <YcCharts 
    :options="chartOptions" 
    height="300px"
    class="custom-chart"
  />
</template>

<style scoped>
.custom-chart :deep(.el-card) {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.custom-chart :deep(.el-card__header) {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  color: white;
}
</style>
```

## 注意事项

1. **性能优化**：大数据量时建议使用 ECharts 的数据采样功能
2. **内存管理**：组件会自动清理事件监听器，无需手动处理
3. **配置更新**：深度监听 `options` 变化，确保图表及时更新
4. **移动端适配**：小屏幕下会自动调整图例和提示框样式

## 常见问题

### Q: 图表不显示？

A: 检查 `options` 配置是否正确，特别是 `series` 数据格式。

### Q: 如何禁用响应式？

A: 设置 `overrideOptions: true` 并提供完整配置。

### Q: 如何自定义加载动画？

A: 组件使用内置的 `loadingOptions`，暂不支持自定义。

### Q: 图表更新不及时？

A: 确保 `options` 是响应式数据，避免直接修改嵌套对象。

### Q: 移动端图表显示不完整？

A: 组件已内置移动端优化，如果仍有问题：
1. 检查容器是否设置了正确的宽度
2. 使用响应式高度设置
3. 确保没有 CSS 样式冲突

### Q: 如何自定义移动端样式？

A: 可以通过媒体查询覆盖默认样式：
```css
@media (max-width: 768px) {
  :deep(.echarts) {
    min-height: 250px !important;
  }
}
```

### Q: 图例在移动端显示异常？

A: 组件会自动调整图例布局，如需自定义：
```javascript
const mobileOptions = {
  legend: {
    orient: 'horizontal',
    top: 'bottom',
    textStyle: { fontSize: 10 }
  }
}
```

## 示例集合

### 柱状图示例

```vue
<template>
  <YcCharts
    title="月度销量统计"
    title-icon="bar-chart"
    :options="barChartOptions"
    height="400px"
  />
</template>

<script setup>
const barChartOptions = {
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  yAxis: {
    type: 'value',
    name: '销量(台)'
  },
  series: [{
    name: '销量',
    type: 'bar',
    data: [120, 200, 150, 80, 70, 110],
    itemStyle: {
      color: '#409EFF'
    }
  }]
}
</script>
```

### 折线图示例

```vue
<template>
  <YcCharts
    title="趋势分析"
    :options="lineChartOptions"
    :loading="loading"
  />
</template>

<script setup>
const loading = ref(false)
const lineChartOptions = {
  xAxis: {
    type: 'category',
    data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '本周',
      type: 'line',
      data: [820, 932, 901, 934, 1290, 1330, 1320],
      smooth: true
    },
    {
      name: '上周',
      type: 'line',
      data: [720, 832, 801, 834, 1190, 1230, 1220],
      smooth: true
    }
  ]
}
</script>
```

### 饼图示例

```vue
<template>
  <YcCharts
    title="市场份额"
    :options="pieChartOptions"
    height="500px"
  />
</template>

<script setup>
const pieChartOptions = {
  series: [{
    name: '市场份额',
    type: 'pie',
    radius: ['40%', '70%'],
    data: [
      { value: 335, name: '产品A' },
      { value: 310, name: '产品B' },
      { value: 234, name: '产品C' },
      { value: 135, name: '产品D' },
      { value: 1548, name: '其他' }
    ],
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowColor: 'rgba(0, 0, 0, 0.5)'
      }
    }
  }]
}
</script>
```

### 混合图表示例

```vue
<template>
  <YcCharts
    title="销量与利润分析"
    :options="mixedChartOptions"
  />
</template>

<script setup>
const mixedChartOptions = {
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  yAxis: [
    {
      type: 'value',
      name: '销量',
      position: 'left'
    },
    {
      type: 'value',
      name: '利润率',
      position: 'right',
      axisLabel: {
        formatter: '{value}%'
      }
    }
  ],
  series: [
    {
      name: '销量',
      type: 'bar',
      data: [120, 200, 150, 80, 70, 110]
    },
    {
      name: '利润率',
      type: 'line',
      yAxisIndex: 1,
      data: [15, 23, 18, 12, 8, 16]
    }
  ]
}
</script>
```

### 堆叠面积图示例

```vue
<template>
  <YcCharts
    title="各产品销量堆叠趋势"
    :options="stackedAreaOptions"
  />
</template>

<script setup>
const stackedAreaOptions = {
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
  },
  yAxis: {
    type: 'value',
    name: '销量(台)'
  },
  series: [
    {
      name: '产品A',
      type: 'line',
      stack: 'Total',
      areaStyle: {},
      emphasis: { focus: 'series' },
      data: [120, 132, 101, 134, 90, 230, 210]
    },
    {
      name: '产品B',
      type: 'line',
      stack: 'Total',
      areaStyle: {},
      emphasis: { focus: 'series' },
      data: [220, 182, 191, 234, 290, 330, 310]
    },
    {
      name: '产品C',
      type: 'line',
      stack: 'Total',
      areaStyle: {},
      emphasis: { focus: 'series' },
      data: [150, 232, 201, 154, 190, 330, 410]
    }
  ]
}
</script>
```

### 簇状柱形图示例

```vue
<template>
  <YcCharts
    title="各季度产品销量对比"
    :options="clusteredBarOptions"
  />
</template>

<script setup>
const clusteredBarOptions = {
  xAxis: {
    type: 'category',
    data: ['Q1', 'Q2', 'Q3', 'Q4']
  },
  yAxis: {
    type: 'value',
    name: '销量(台)'
  },
  series: [
    {
      name: '产品A',
      type: 'bar',
      data: [120, 200, 150, 80],
      itemStyle: { color: '#5470c6' }
    },
    {
      name: '产品B',
      type: 'bar',
      data: [100, 180, 120, 90],
      itemStyle: { color: '#91cc75' }
    },
    {
      name: '产品C',
      type: 'bar',
      data: [80, 160, 100, 70],
      itemStyle: { color: '#fac858' }
    }
  ]
}
</script>
```

### 堆积柱形图示例

```vue
<template>
  <YcCharts
    title="各月份产品销量构成"
    :options="stackedBarOptions"
  />
</template>

<script setup>
const stackedBarOptions = {
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  yAxis: {
    type: 'value',
    name: '销量(台)'
  },
  series: [
    {
      name: '产品A',
      type: 'bar',
      stack: 'total',
      emphasis: { focus: 'series' },
      data: [120, 132, 101, 134, 90, 230]
    },
    {
      name: '产品B',
      type: 'bar',
      stack: 'total',
      emphasis: { focus: 'series' },
      data: [220, 182, 191, 234, 290, 330]
    },
    {
      name: '产品C',
      type: 'bar',
      stack: 'total',
      emphasis: { focus: 'series' },
      data: [150, 232, 201, 154, 190, 330]
    }
  ]
}
</script>
```

## API 参考

### 方法

组件通过 `ref` 暴露了 ECharts 实例的方法：

```vue
<template>
  <YcCharts ref="chartRef" :options="chartOptions" />
</template>

<script setup>
const chartRef = ref(null)

// 获取 ECharts 实例
const getChartInstance = () => {
  return chartRef.value?.chart
}

// 手动触发重绘
const resize = () => {
  chartRef.value?.chart?.resize()
}

// 获取图表图片
const getDataURL = () => {
  return chartRef.value?.chart?.getDataURL({
    type: 'png',
    backgroundColor: '#fff'
  })
}
</script>
```

### 事件处理

```vue
<template>
  <YcCharts
    ref="chartRef"
    :options="chartOptions"
    @click="handleChartClick"
  />
</template>

<script setup>
const chartRef = ref(null)

onMounted(() => {
  const chart = chartRef.value?.chart
  if (chart) {
    // 监听点击事件
    chart.on('click', (params) => {
      console.log('点击了:', params)
    })

    // 监听图例选择事件
    chart.on('legendselectchanged', (params) => {
      console.log('图例变化:', params)
    })
  }
})
</script>
```

## 主题定制

### 自定义颜色主题

```vue
<script setup>
// 覆盖默认颜色
const customChartOptions = {
  color: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'],
  series: [
    // ... 你的系列配置
  ]
}
</script>
```

### 深色主题

```vue
<template>
  <YcCharts
    :options="darkThemeOptions"
    :override-options="true"
    class="dark-chart"
  />
</template>

<script setup>
const darkThemeOptions = {
  backgroundColor: '#1e1e1e',
  textStyle: {
    color: '#fff'
  },
  // ... 其他深色主题配置
}
</script>

<style scoped>
.dark-chart :deep(.el-card) {
  background-color: #1e1e1e;
  border-color: #333;
}
</style>
```

## 性能优化建议

### 大数据量处理

```vue
<script setup>
// 使用数据采样
const largeDataOptions = {
  series: [{
    type: 'line',
    sampling: 'average', // 数据采样
    large: true, // 开启大数据量优化
    data: largeDataArray
  }]
}

// 使用数据缩放
const zoomOptions = {
  dataZoom: [
    {
      type: 'inside',
      start: 0,
      end: 20
    },
    {
      type: 'slider',
      start: 0,
      end: 20
    }
  ]
}
</script>
```

### 按需加载图表类型

```javascript
// 只导入需要的图表类型
import { BarChart, LineChart } from 'echarts/charts'
import { GridComponent, TooltipComponent } from 'echarts/components'
import { use } from 'echarts/core'

use([BarChart, LineChart, GridComponent, TooltipComponent])
```

## 移动端最佳实践

### 1. 响应式高度设置

```vue
<script setup>
import { ref, computed } from 'vue'

const windowWidth = ref(window.innerWidth)

const chartHeight = computed(() => {
  if (windowWidth.value <= 480) return '250px'
  if (windowWidth.value <= 768) return '300px'
  if (windowWidth.value <= 1024) return '350px'
  return '400px'
})
</script>
```

### 2. 容器样式优化

```css
.chart-container {
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

@media (max-width: 768px) {
  .chart-container {
    padding: 8px;
    margin: 0;
  }
}
```

### 3. 数据简化

移动端建议简化数据展示：

```javascript
const mobileData = computed(() => {
  if (isMobile.value) {
    // 移动端只显示前5项数据
    return originalData.value.slice(0, 5)
  }
  return originalData.value
})
```

## 更新日志

- **v1.0.0**: 初始版本，支持基础图表功能
- **v1.1.0**: 添加响应式支持和智能图例
- **v1.2.0**: 优化性能，修复内存泄漏问题
- **v1.3.0**: 增加事件处理和方法暴露
- **v1.4.0**: 完善文档和示例
- **v1.5.0**: 全面移动端优化，新增多种图表类型
  - 新增堆叠面积图、簇状柱形图、堆积柱形图支持
  - 完善移动端响应式系统
  - 优化触摸交互体验
  - 智能布局自动调整
