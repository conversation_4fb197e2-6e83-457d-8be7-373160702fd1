import request from '@/utils/request'
import { color } from '@/views/components/echarts/config.js'

import { getYAxisName, changeDataType } from '@/views/ambience/components/commonConfigData'
import { ElMessage } from 'element-plus'
import { splitDate } from './macro'
import { changeYearMonth, JI, sortMonth, getYearMonthBu } from './market'
import { numberFormat } from '@/utils/format.js'

// derv//汽油销量
// dervProp
// electricity //纯动力
// electricityProp
// fuelcell // 燃料电池
// fuelcellProp
// gas // 气体销量
// gasProp
// mixture
// mixtureProp // 混合动力份额

const keyProp = {
  derv: '汽油',
  electricity: '纯电动',
  fuelcell: '燃料电池',
  gas: '气体', //
  mixture: '混合动力'
}

const changeData = dataRes => {
  let obj = {}
  let obj2 = {}
  let ysm = new Set()
  const _yms = dataRes.map(item => {
    let { year, month, ...otherItem } = item
    const entriesValues = Object.entries(otherItem)
    // console.log(entriesValues,'entriesValues')
    month = month && month * 1 >= 10 ? month : `0${month}`
    entriesValues?.forEach(i => {
      const [key, val] = i
      const _key = key
      // console.log(key.indexOf('Prop'),'key.indexOf('Prop')')
      if (key.indexOf('Prop') > -1) {
        const _obj2 = obj2[_key] ? obj2[_key] : []
        obj2 = {
          ...obj2,
          [_key]: [
            ..._obj2,
            {
              slice: val,
              yearMonth: `${year}'${month}`
            }
          ]
        }
      } else {
        const _obj = obj[_key] ? obj[_key] : []
        obj = {
          // ...obj,
          // [_key]:[..._obj,val]
          ...obj,
          [_key]: [
            ..._obj,
            {
              slice: val,
              yearMonth: `${year}'${month}`
            }
          ]
        }
      }
    })

    ysm.add(`${year}'${month}`)
    return `${year}'${month}` //`${year}年${month}月`
  })
  let _ysm = [...ysm]
  const newYearMonth = sortMonth(_ysm)
  let _newYearMonth = changeYearMonth(newYearMonth)

  const valueData = Object.entries(obj)
  const newReas = valueData?.map(item => {
    const [key, data] = item
    let saleslistTotal = []
    const saleslist = newYearMonth?.map((y, index) => {
      const list = data?.filter(({ yearMonth: m = '' }) => m === y)
      let _S = ''
      if (list && list.length > 0) {
        _S = `${list[0]?.slice}` || list[0]?.lsice || 0
      }
      saleslistTotal.push({
        name: _newYearMonth[index],
        value: _S / 10000
      })
      return _S
    })

    return {
      // 每条线的名称
      name: keyProp[key],
      // 每条线的数据
      data: saleslistTotal, //saleslist,
      // 展示成什么图形
      type: 'bar',
      stack: 'total',
      barWidth: '40%'
    }
  })

  const chart1 = {
    barTotal: true,
    height: '30%',
    title: {
      text: '燃料结构走势'
    },
    xAxis: {
      data: changeYearMonth(newYearMonth)
    },
    yAxis: {
      type: 'value',
      // max: 100,
      name: getYAxisName('万台')
    },
    grid: { left: 56, bottom: 60, right: 85, top: 46 },
    series: newReas
  }

  const valueData2 = Object.entries(obj2)
  const newReas1 = valueData2?.map((_item, index) => {
    const [name, data] = _item
    let saleslistTotal = []
    const saleslist = newYearMonth?.map((y, index) => {
      let _S = ''
      const list = data?.filter(({ yearMonth: m = '' }) => m === y)
      if (list && list.length > 0) {
        _S = `${list[0]?.slice}` || list[0]?.lsice || 0
      }

      saleslistTotal.push({
        name: _newYearMonth[index],
        value: _S
      })

      return _S
    })

    let _name = name?.replace(new RegExp('Prop', 'g'), '') || ''
    let itemObj = {
      type: 'bar',
      data: saleslistTotal, //saleslist,//[30, 50, 44, 25, 30, 20, 10, 30, 20, 20, 10, 25],
      name: keyProp[_name],
      stack: 'total',
      barWidth: '40%'
    }

    return itemObj
  })

  const chart2 = {
    height: '30%',
    title: {
      text: '燃料结构份额走势'
    },
    xAxis: {
      data: changeYearMonth(newYearMonth)
    },
    yAxis: {
      type: 'value',
      max: 100,
      name: getYAxisName('%')
    },
    grid: { left: 56, bottom: 60, right: 85, top: 46 },
    series: newReas1
  }

  return {
    chart1,
    chart2
  }
}

// 8.1	销量/占比
export const getNewPatternList = async query => {
  const { date = undefined, dataType = '', ...otherQuery } = query
  const newDate = splitDate(date)
  // console.log(newDate,'newDate')
  const _dataType = changeDataType(dataType)
  const params = {
    ...newDate,
    dataType: _dataType,
    ...otherQuery
  }

  try {
    const res = await request({
      url: '/intelligence/marketEnv/newEnergyList',
      method: 'post',
      data: params
    })
    const { code = '', data = [], msg = '' } = res
    // console.log(data,'data')
    if (code === 200) {
      const _res = changeData(data)
      // console.log(_res, 'data')
      return _res
    } else {
      ElMessage({
        showClose: true,
        message: msg,
        type: 'error'
      })
      return []
    }
  } catch (error) {
    console.log(error, 'error')
    return []
  }
}

const changeTrendList = (resData = [], date) => {
  let resData1 = resData.filter(({ manuFacturer }) => manuFacturer !== '合计')
  //  console.log(resDataindex,'resDataindex')
  //  let resData2 = resData1.filter(({ manuFacturer = '' })=>(manuFacturer !== '其他'))
  //  let resData3 = resData1.filter(({ manuFacturer = '' })=>(manuFacturer === '其他'))
  //  if(resData3 && resData3.length >1){
  //   resData1 = [...resData2,{...resData3[1]}]
  //  }
  const newReas = resData1?.map(item => {
    const { manuFacturer, nowSales } = item
    return {
      name: manuFacturer,
      value: nowSales,
      nowProp: item?.nowProp
    }
  })

  const pieOptionsCar = {
    height: '80%',
    tooltip: {
      trigger: 'item'
    },
    legend: {
      show: false
    },
    title: {
      text: `企业竞争格局(辆，%)`
    },
    xAxis: {
      show: false
    },
    yAxis: {
      show: false
    },
    series: [
      {
        name: '新能源',
        type: 'pie',
        radius: '50%',
        itemStyle: {
          color: undefined
        },
        label: {
          formatter: params => {
            const { value, name, percent } = params
            const data = resData.filter(({ manuFacturer: n = '' }) => n === name)
            let prop = ''
            if (data.length) {
              prop = data[0]?.nowProp || ''
            }
            const result = numberFormat(prop)
            const _value = numberFormat(value, 0)

            return `${name},${_value},${result}%`
          },
          rich: {
            time: {
              fontSize: 10,
              color: '#999'
            }
          }
        },
        data: newReas
      }
    ]
  }

  const list = resData.filter(({ manuFacturer }) => manuFacturer === '合计')

  const obj = {
    pieOptionsCar,
    list: [...resData1, ...list]
  }

  return obj
}

// 8.1	饼图
export const getMarketEnvPatternList = async query => {
  const { date = undefined, dataType = '', ...otherQuery } = query

  // return
  const _dataType = changeDataType(dataType)
  // let newDate = {}

  // if(date){
  //   let [key1,key2 = '1'] = date.split("-")
  //   newDate = {
  //     year:key1,
  //     month:`${Number(key2)}`
  //   }
  // }
  const params = {
    dataType: _dataType,
    ...otherQuery
  }

  try {
    const res = await request({
      url: '/intelligence/marketEnv/patternList',
      method: 'post',
      data: params
    })
    const { code = '', data = [], msg = '' } = res
    // console.log(data,'data')
    if (code === 200) {
      const _time = `${params?.year}年${params?.month}月`
      const _res = changeTrendList(data, _time)
      return _res
    } else {
      ElMessage({
        showClose: true,
        message: msg,
        type: 'error'
      })
      return []
    }
  } catch (error) {
    console.log(error, 'error')
    return []
  }
}

export const newEnergyBarChartData = async (params={}) => {
  try {
    console.log('发起请求')
    const res = await request({
      url: '/intelligence/marketEnv/newEnergyBarChartData',
      method: 'POST',
      data: params
    })
    console.log('请求参数res', res)
    const { code = '', data = [], msg = '' } = res
    if (code === 200) {
      return data
    } else {
      ElMessage({
        showClose: true,
        message: msg,
        type: 'error'
      })
      return []
    }
  } catch (error) {
    console.log(error, 'error')
    return []
  }
}
