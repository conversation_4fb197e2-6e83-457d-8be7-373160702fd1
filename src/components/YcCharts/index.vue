<template>
  <el-card>
    <template #header>
      <slot name="title">
        <block-title :title="props.title" :icon="props.titleIcon" />
      </slot>
    </template>
    <div class="fig">
      <v-chart
        :key="chartKey"
        :loadingOptions="loadingOptions"
        ref="chart"
        :autoresize="true"
        :loading="loading"
        :option="mergedOptions"
        :update-options="mergedOptions"
        manual-update
        class="echarts"
      >
    
    </v-chart>
    </div>
  </el-card>
</template>
<script setup>
import BlockTitle from '@/views/components/BlockTitle.vue'
import VChart, { THEME_KEY } from 'vue-echarts'
import { ref, computed, shallowRef, watch, onUnmounted } from 'vue'
import { color } from '@/views/components/echarts/config.js'
import 'echarts'
import { TooltipFormatter } from '@/utils/common/method'
import { TooltipComponent } from '@/views/components/jsx/TooltipComponent'

provide(THEME_KEY, 'light') // 修复拼写错误

const chartKey = ref(`${Date.now()}`)
const chart = shallowRef(null)

// 监听窗口大小变化
const windowWidth = ref(window.innerWidth)
const updateWindowWidth = () => {
  windowWidth.value = window.innerWidth
}

// 添加窗口大小监听器
window.addEventListener('resize', updateWindowWidth)

const loadingOptions = {
  text: 'Loading...',
  color: '#000',
  textColor: '#fff',
  maskColor: '#FFF'
}

// 定义组件接收的属性（props）
const props = defineProps({
  // 标题文本
  title: {
    type: String,
    default: ''
  },
  // 标题左侧图标
  titleIcon: {
    type: String,
    default: 'data1'
  },
  // 图表配置项（必填）
  options: {
    type: Object,
    required: true
  },
  // 是否全量覆盖基础配置
  overrideOptions: {
    type: Boolean,
    default: false
  },
  // 是否显示加载动画
  loading: {
    type: Boolean,
    default: false
  },
  // 图表高度
  height: {
    type: [String, Number],
    default: '50vh'
  }
})

// 基础配置项
const baseOptions = ref({
  color,
  tooltip: {
    confine: true, // 限制 tooltip 在图表区域内
    // formatter: params =>
    //   TooltipFormatter(TooltipComponent, params, {
    //     shouldSort: false
    //   })
        formatter: '{b}: {c}',

  },
  grid: {
    left: '5%',
    right: '14%',
    top: '8%',
    bottom: '4%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    axisTick: {
      show: true, //显示x轴刻度
      alignWithLabel: true //刻度线与标签对齐
    }
  },
  yAxis: {
    type: 'category',
    axisTick: {
      show: true, //显示x轴刻度
      alignWithLabel: true //刻度线与标签对齐
    }
  },
  legend: {
    right: 'right', // 放置在右侧
    top: 'middle',
    bottom: 0,
    type: 'scroll',
    orient: 'vertical'
  },
  series: [],
  media: [
    {
      // 平板设备配置
      query: { maxWidth: 1024, minWidth: 769 },
      option: {
        grid: {
          left: '8%',
          right: '8%',
          top: '12%',
          bottom: '12%',
          containLabel: true
        },
        legend: {
          orient: 'horizontal',
          top: 'top',
          left: 'center'
        }
      }
    },
    {
      // 手机设备配置
      query: { maxWidth: 768 },
      option: {
        grid: {
          left: '5%',
          right: '5%',
          top: '15%',
          bottom: '15%',
          containLabel: true
        },
        legend: {
          orient: 'horizontal',
          top: 'top',
          left: 'center',
          itemWidth: 12,
          itemHeight: 8,
          textStyle: {
            fontSize: 10
          }
        },
        tooltip: {
          formatter: params => {
            if (Array.isArray(params)) {
              let result = `<div style="font-size: 12px;"><strong>${params[0].name}</strong><br/>`
              params.forEach(param => {
                result += `<div style="color: ${param.color};">● ${param.seriesName}: ${param.value}</div>`
              })
              result += '</div>'
              return result
            } else {
              return `<div style="font-size: 12px; color: #409EFF; font-weight: bold;">${params.name}: ${params.value}</div>`
            }
          },
          confine: true,
          enterable: true,
          extraCssText: 'max-width: 80vw; white-space: pre-line; word-break: break-all; font-size: 12px;'
        },
        xAxis: {
          axisLabel: {
            fontSize: 10,
            rotate: 0,
            interval: 0
          }
        },
        yAxis: {
          axisLabel: {
            fontSize: 10
          },
          nameTextStyle: {
            fontSize: 10
          }
        }
      }
    },
    {
      // 小屏手机配置
      query: { maxWidth: 480 },
      option: {
        grid: {
          left: '8%',
          right: '8%',
          top: '20%',
          bottom: '20%',
          containLabel: true
        },
        legend: {
          orient: 'horizontal',
          top: 'top',
          left: 'center',
          itemWidth: 10,
          itemHeight: 6,
          textStyle: {
            fontSize: 9
          },
          itemGap: 8
        },
        xAxis: {
          axisLabel: {
            fontSize: 9,
            rotate: 45,
            interval: 0
          }
        },
        yAxis: {
          axisLabel: {
            fontSize: 9
          },
          nameTextStyle: {
            fontSize: 9
          }
        }
      }
    }
  ]
})

// 合并配置项
const mergedOptions = computed(() => {
  if (props.overrideOptions) {
    return { ...props.options } // 全量覆盖
  }

  const merged = {
    ...baseOptions.value,
    ...props.options,
    yAxis: props.options.yAxis
  }

  // 动态应用媒体查询配置
  applyMediaQuery(merged)
  // 通过series配置项，动态应用legend
  merged.legend = applyDynamicLegend(merged)

  return merged
})

// 监听配置变化，更新图表key
watch(
  () => props.options,
  () => {
    chartKey.value = Date.now()
  },
  { deep: true }
)

// 监听窗口大小变化，重新计算配置
watch(windowWidth, () => {
  chartKey.value = Date.now()
})

// 组件卸载时清理事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', updateWindowWidth)
})

/**
 * 动态应用图例配置
 * @param {Object} options
 */
const applyDynamicLegend = options => {
  if (!Array.isArray(options.series)) {
    return options.legend || {}
  }

  // 提取所有有效的 legend 名称
  const legendNames = options.series
    .filter(series => series.name) // 过滤掉没有名称的系列
    .map(series => series.name)

  // 去重并更新 legend 配置
  const uniqueLegendNames = [...new Set(legendNames)]

  // 返回更新后的 legend 配置
  return {
    ...options.legend,
    data: uniqueLegendNames
  }
}

// 应用媒体查询配置
const applyMediaQuery = options => {
  const currentWidth = windowWidth.value // 使用响应式的窗口宽度
  const mediaConfig = baseOptions.value.media || []

  for (const media of mediaConfig) {
    const query = media.query
    if (
      (!query.maxWidth || currentWidth <= query.maxWidth) &&
      (!query.minWidth || currentWidth >= query.minWidth)
    ) {
      mergeDeep(options, media.option)
      break
    }
  }
}

// 深度合并函数
const mergeDeep = (target, source) => {
  for (const key in source) {
    if (source[key] instanceof Object && key in target && target[key] instanceof Object) {
      mergeDeep(target[key], source[key])
    } else {
      target[key] = source[key]
    }
  }
}
</script>

<style>
@import '@/assets/styles/bi/variables.module.scss';

:deep(.el-card__body) {
  padding: 0 6px 6px !important;
}

:deep(.el-card__header) {
  padding: 0;
}

.fig {
  display: flex;
  justify-content: center;
  width: 100%;
  overflow: hidden;
  height: v-bind(height);

  .echarts {
    width: 100% !important;
    height: 100% !important;
    min-height: 300px;
  }
}

/* 平板设备 */
@media (max-width: 1024px) {
  .fig {
    height: v-bind(height);

    .echarts {
      min-height: 300px;
      height: 100% !important;
    }
  }
}

/* 手机设备 */
@media (max-width: 768px) {
  :deep(.el-card) {
    margin: 0;
    border-radius: 0;
  }

  :deep(.el-card__body) {
    padding: 8px !important;
  }

  :deep(.el-card__header) {
    padding: 8px 12px;
  }

  .fig {
    width: 100%;
    height: v-bind(height);
    max-height: 80vh; /* 防止在小屏幕上过高 */

    .echarts {
      width: 100% !important;
      height: 100% !important;
      min-height: 250px;
    }
  }
}

/* 小屏手机 */
@media (max-width: 480px) {
  .fig {
    height: v-bind(height);
    max-height: 70vh; /* 小屏幕进一步限制高度 */

    .echarts {
      min-height: 200px;
      height: 100% !important;
    }
  }
}
</style>
