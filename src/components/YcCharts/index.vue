<template>
  <el-card>
    <template #header>
      <slot name="title">
        <block-title :title="props.title" :icon="props.titleIcon" />
      </slot>
    </template>
    <div class="fig">
      <v-chart
        :key="chartKey"
        :loadingOptions="loadingOptions"
        ref="chart"
        :autoresize="true"
        :loading="loading"
        :option="mergedOptions"
        :update-options="mergedOptions"
        manual-update
      />
    </div>
  </el-card>
</template>
<script setup>
import BlockTitle from '@/views/components/BlockTitle.vue'
import VChart, { THEME_KEY } from 'vue-echarts'
import { ref, computed, shallowRef, watch, onUnmounted } from 'vue'
import { color } from '@/views/components/echarts/config.js'
import 'echarts'
import { TooltipFormatter } from '@/utils/common/method'
import { TooltipComponent } from '@/views/components/jsx/TooltipComponent'

provide(THEME_KEY, 'light') // 修复拼写错误

const chartKey = ref(`${Date.now()}`)
const chart = shallowRef(null)

// 监听窗口大小变化
const windowWidth = ref(window.innerWidth)
const updateWindowWidth = () => {
  windowWidth.value = window.innerWidth
}

// 添加窗口大小监听器
window.addEventListener('resize', updateWindowWidth)

const loadingOptions = {
  text: 'Loading...',
  color: '#000',
  textColor: '#fff',
  maskColor: '#FFF'
}

// 定义组件接收的属性（props）
const props = defineProps({
  // 标题文本
  title: {
    type: String,
    default: ''
  },
  // 标题左侧图标
  titleIcon: {
    type: String,
    default: 'data1'
  },
  // 图表配置项（必填）
  options: {
    type: Object,
    required: true
  },
  // 是否全量覆盖基础配置
  overrideOptions: {
    type: Boolean,
    default: false
  },
  // 是否显示加载动画
  loading: {
    type: Boolean,
    default: false
  },
  // 图表高度
  height: {
    type: [String, Number],
    default: '50vh'
  }
})

// 基础配置项
const baseOptions = ref({
  color,
  tooltip: {
    confine: true, // 限制 tooltip 在图表区域内
    formatter: params =>
      TooltipFormatter(TooltipComponent, params, {
        shouldSort: false
      })
  },
  grid: {
    left: '5%',
    right: '14%',
    top: '8%',
    bottom: '8%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    axisTick: {
      show: true, //显示x轴刻度
      alignWithLabel: true //刻度线与标签对齐
    }
  },
  yAxis: {
    type: 'category',
    axisTick: {
      show: true, //显示x轴刻度
      alignWithLabel: true //刻度线与标签对齐
    }
  },
  legend: {
    right: 'right', // 放置在右侧
    top: 'middle',
    bottom: 0,
    type: 'scroll',
    orient: 'vertical'
  },
  series: [],
  media: [
    {
      // 默认配置（小屏幕）
      query: { maxWidth: 768 },
      option: {
        legend: {},
        tooltip: {
          formatter: params => {
            return `<div style="color: #409EFF; font-weight: bold;">${params.name}: ${params.value}</div>`
          },
          confine: true, // 限制 tooltip 在图表区域内
          enterable: true // 允许鼠标进入 tooltip
          // 针对移动端优化
          // extraCssText: 'max-width: 90vw; white-space: pre-line; word-break: break-all;',
        }
      }
    }
  ]
})

// 合并配置项
const mergedOptions = computed(() => {
  if (props.overrideOptions) {
    return { ...props.options } // 全量覆盖
  }

  const merged = {
    ...baseOptions.value,
    ...props.options,
    yAxis: props.options.yAxis
  }

  // 动态应用媒体查询配置
  applyMediaQuery(merged)
  // 通过series配置项，动态应用legend
  merged.legend = applyDynamicLegend(merged)

  return merged
})

// 监听配置变化，更新图表key
watch(
  () => props.options,
  () => {
    chartKey.value = Date.now()
  },
  { deep: true }
)

// 监听窗口大小变化，重新计算配置
watch(windowWidth, () => {
  chartKey.value = Date.now()
})

// 组件卸载时清理事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', updateWindowWidth)
})

/**
 * 动态应用图例配置
 * @param {Object} options
 */
const applyDynamicLegend = options => {
  if (!Array.isArray(options.series)) {
    return options.legend || {}
  }

  // 提取所有有效的 legend 名称
  const legendNames = options.series
    .filter(series => series.name) // 过滤掉没有名称的系列
    .map(series => series.name)

  // 去重并更新 legend 配置
  const uniqueLegendNames = [...new Set(legendNames)]

  // 返回更新后的 legend 配置
  return {
    ...options.legend,
    data: uniqueLegendNames
  }
}

// 应用媒体查询配置
const applyMediaQuery = options => {
  const currentWidth = windowWidth.value // 使用响应式的窗口宽度
  const mediaConfig = baseOptions.value.media || []

  for (const media of mediaConfig) {
    const query = media.query
    if (
      (!query.maxWidth || currentWidth <= query.maxWidth) &&
      (!query.minWidth || currentWidth >= query.minWidth)
    ) {
      mergeDeep(options, media.option)
      break
    }
  }
}

// 深度合并函数
const mergeDeep = (target, source) => {
  for (const key in source) {
    if (source[key] instanceof Object && key in target && target[key] instanceof Object) {
      mergeDeep(target[key], source[key])
    } else {
      target[key] = source[key]
    }
  }
}
</script>

<style>
@import '@/assets/styles/bi/variables.module.scss';

:deep(.el-card__body) {
  padding: 0 6px 6px !important;
}

:deep(.el-card__header) {
  padding: 0;
}

.fig {
  display: flex;
  justify-content: center;
  width: fit-content;
  width: 100%;

  .echarts {
    /* width: calc(100% + 4em); */
    height: v-bind(height);
  }
}

@media (max-width: 980px) {
  .fig {
    width: 100vw;
    height: 40vh;

    .echarts {
      width: 100%;
      min-width: 0;
      /* height: 60vh; */
      border: none;
      border-radius: 0;
      box-shadow: none;
    }
  }
}
</style>
