<template>
  <div class="demo-container">
    <h3>YcCharts 组件演示</h3>
    
    <!-- 基础柱状图 -->
    <div class="demo-section">
      <p>基础柱状图 - 固定高度500px (默认 tooltip)</p>
      <YcCharts
        title="月度销量统计"
        :options="barChartOptions"
        height="500px"
      />
    </div>

    <!-- 测试自定义 tooltip -->
    <div class="demo-section">
      <p>自定义 Tooltip 测试</p>
      <YcCharts
        title="自定义提示框"
        :options="barChartOptions"
        :use-custom-tooltip="true"
        height="300px"
      />
    </div>

    <!-- 测试 tooltip 配置覆盖 -->
    <div class="demo-section">
      <p>自定义 Tooltip 配置测试</p>
      <YcCharts
        title="自定义配置提示框"
        :options="customTooltipOptions"
        height="300px"
      />
    </div>
    <!-- 折线图 -->
    <div class="demo-section">
      <h2>趋势折线图</h2>
      <YcCharts
        title="销量趋势"
        :options="lineChartOptions"
        :loading="lineLoading"
        :height="chartHeight"
      />
      <el-button @click="updateLineData" :loading="lineLoading">
        更新数据
      </el-button>
    </div>

    <!-- 饼图 -->
    <div class="demo-section">
      <h2>市场份额饼图</h2>
      <YcCharts
        title="产品市场份额"
        :options="pieChartOptions"
        :height="pieChartHeight"
      />
    </div>

    <!-- 混合图表 -->
    <div class="demo-section">
      <h2>混合图表</h2>
      <YcCharts
        title="销量与利润分析"
        :options="mixedChartOptions"
        :height="chartHeight"
      />
    </div>

    <!-- 自定义标题插槽 -->
    <div class="demo-section">
      <h2>自定义标题</h2>
      <YcCharts :options="customTitleOptions" :height="chartHeight">
        <template #title>
          <div class="custom-title">
            <el-icon><TrendCharts /></el-icon>
            <span>自定义标题样式</span>
            <el-tag type="success" size="small">实时</el-tag>
          </div>
        </template>
      </YcCharts>
    </div>

    <!-- 完全自定义配置 -->
    <div class="demo-section">
      <h2>完全自定义配置</h2>
      <YcCharts
        :options="fullCustomOptions"
        :override-options="true"
        :height="chartHeight"
      />
    </div>

    <!-- 堆叠面积图 -->
    <div class="demo-section">
      <h2>堆叠面积图</h2>
      <YcCharts
        title="各产品销量堆叠趋势"
        :options="stackedAreaOptions"
        :height="chartHeight"
      />
    </div>

    <!-- 簇状柱形图 -->
    <div class="demo-section">
      <h2>簇状柱形图（分组柱状图）</h2>
      <YcCharts
        title="各季度产品销量对比"
        :options="clusteredBarOptions"
        :height="chartHeight"
      />
    </div>

    <!-- 堆积柱形图 -->
    <div class="demo-section">
      <h2>堆积柱形图</h2>
      <YcCharts
        title="各月份产品销量构成"
        :options="stackedBarOptions"
        :height="chartHeight"
      />
    </div>

    <!-- 百分比堆积柱形图 -->
    <div class="demo-section">
      <h2>百分比堆积柱形图</h2>
      <YcCharts
        title="各月份产品销量占比"
        :options="percentStackedBarOptions"
        :height="chartHeight"
      />
    </div>

    <!-- 堆叠折线图 -->
    <div class="demo-section">
      <h2>堆叠折线图</h2>
      <YcCharts
        title="各渠道销量堆叠趋势"
        :options="stackedLineOptions"
        :height="chartHeight"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import YcCharts from '@/components/YcCharts/index.vue'
import { TrendCharts } from '@element-plus/icons-vue'

// 响应式窗口尺寸
const windowWidth = ref(window.innerWidth)

// 响应式图表高度
const chartHeight = computed(() => {
  if (windowWidth.value <= 480) {
    return '250px'  // 小屏手机
  } else if (windowWidth.value <= 768) {
    return '300px'  // 手机
  } else if (windowWidth.value <= 1024) {
    return '350px'  // 平板
  } else {
    return '400px'  // 桌面
  }
})

// 饼图专用高度（通常需要更高一些）
const pieChartHeight = computed(() => {
  if (windowWidth.value <= 480) {
    return '280px'  // 小屏手机
  } else if (windowWidth.value <= 768) {
    return '350px'  // 手机
  } else if (windowWidth.value <= 1024) {
    return '400px'  // 平板
  } else {
    return '500px'  // 桌面
  }
})

// 窗口大小变化监听
const handleResize = () => {
  windowWidth.value = window.innerWidth
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 基础柱状图配置
const barChartOptions = ref({
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  yAxis: {
    type: 'value',
    name: '销量(台)'
  },
  series: [{
    name: '销量',
    type: 'bar',
    data: [120, 200, 150, 80, 70, 110],
    itemStyle: {
      color: '#409EFF'
    }
  }]
})

// 折线图配置
const lineLoading = ref(false)
const lineChartOptions = ref({
  xAxis: {
    type: 'category',
    data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '本周',
      type: 'line',
      data: [820, 932, 901, 934, 1290, 1330, 1320],
      smooth: true
    },
    {
      name: '上周',
      type: 'line', 
      data: [720, 832, 801, 834, 1190, 1230, 1220],
      smooth: true
    }
  ]
})

// 更新折线图数据
const updateLineData = async () => {
  lineLoading.value = true
  
  // 模拟异步数据获取
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  lineChartOptions.value.series[0].data = Array.from(
    { length: 7 }, 
    () => Math.floor(Math.random() * 1000) + 500
  )
  lineChartOptions.value.series[1].data = Array.from(
    { length: 7 }, 
    () => Math.floor(Math.random() * 1000) + 400
  )
  
  lineLoading.value = false
}

// 饼图配置
const pieChartOptions = ref({
  series: [{
    name: '市场份额',
    type: 'pie',
    radius: ['40%', '70%'],
    data: [
      { value: 335, name: '产品A' },
      { value: 310, name: '产品B' },
      { value: 234, name: '产品C' },
      { value: 135, name: '产品D' },
      { value: 1548, name: '其他' }
    ],
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowColor: 'rgba(0, 0, 0, 0.5)'
      }
    }
  }]
})

// 混合图表配置
const mixedChartOptions = ref({
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  yAxis: [
    {
      type: 'value',
      name: '销量',
      position: 'left'
    },
    {
      type: 'value',
      name: '利润率',
      position: 'right',
      axisLabel: {
        formatter: '{value}%'
      }
    }
  ],
  series: [
    {
      name: '销量',
      type: 'bar',
      data: [120, 200, 150, 80, 70, 110]
    },
    {
      name: '利润率',
      type: 'line',
      yAxisIndex: 1,
      data: [15, 23, 18, 12, 8, 16]
    }
  ]
})

// 自定义标题图表配置
const customTitleOptions = ref({
  series: [{
    type: 'line',
    data: [10, 20, 30, 25, 35, 40],
    smooth: true
  }]
})

// 完全自定义配置
const fullCustomOptions = ref({
  backgroundColor: '#f5f5f5',
  title: {
    text: '完全自定义图表',
    left: 'center',
    textStyle: {
      color: '#333',
      fontSize: 18
    }
  },
  xAxis: {
    type: 'category',
    data: ['A', 'B', 'C', 'D', 'E'],
    axisLine: {
      lineStyle: { color: '#666' }
    }
  },
  yAxis: {
    type: 'value',
    axisLine: {
      lineStyle: { color: '#666' }
    }
  },
  series: [{
    type: 'bar',
    data: [10, 20, 30, 25, 35],
    itemStyle: {
      color: '#67C23A'
    }
  }]
})

// 堆叠面积图配置
const stackedAreaOptions = ref({
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
  },
  yAxis: {
    type: 'value',
    name: '销量(台)'
  },
  series: [
    {
      name: '产品A',
      type: 'line',
      stack: 'Total',
      areaStyle: {},
      emphasis: {
        focus: 'series'
      },
      data: [120, 132, 101, 134, 90, 230, 210]
    },
    {
      name: '产品B',
      type: 'line',
      stack: 'Total',
      areaStyle: {},
      emphasis: {
        focus: 'series'
      },
      data: [220, 182, 191, 234, 290, 330, 310]
    },
    {
      name: '产品C',
      type: 'line',
      stack: 'Total',
      areaStyle: {},
      emphasis: {
        focus: 'series'
      },
      data: [150, 232, 201, 154, 190, 330, 410]
    },
    {
      name: '产品D',
      type: 'line',
      stack: 'Total',
      areaStyle: {},
      emphasis: {
        focus: 'series'
      },
      data: [320, 332, 301, 334, 390, 330, 320]
    }
  ]
})

// 簇状柱形图配置（分组柱状图）
const clusteredBarOptions = ref({
  xAxis: {
    type: 'category',
    data: ['Q1', 'Q2', 'Q3', 'Q4']
  },
  yAxis: {
    type: 'value',
    name: '销量(台)'
  },
  series: [
    {
      name: '产品A',
      type: 'bar',
      data: [120, 200, 150, 80],
      itemStyle: {
        color: '#5470c6'
      }
    },
    {
      name: '产品B',
      type: 'bar',
      data: [100, 180, 120, 90],
      itemStyle: {
        color: '#91cc75'
      }
    },
    {
      name: '产品C',
      type: 'bar',
      data: [80, 160, 100, 70],
      itemStyle: {
        color: '#fac858'
      }
    },
    {
      name: '产品D',
      type: 'bar',
      data: [60, 140, 80, 50],
      itemStyle: {
        color: '#ee6666'
      }
    }
  ]
})

// 堆积柱形图配置
const stackedBarOptions = ref({
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  yAxis: {
    type: 'value',
    name: '销量(台)'
  },
  series: [
    {
      name: '产品A',
      type: 'bar',
      stack: 'total',
      emphasis: {
        focus: 'series'
      },
      data: [120, 132, 101, 134, 90, 230]
    },
    {
      name: '产品B',
      type: 'bar',
      stack: 'total',
      emphasis: {
        focus: 'series'
      },
      data: [220, 182, 191, 234, 290, 330]
    },
    {
      name: '产品C',
      type: 'bar',
      stack: 'total',
      emphasis: {
        focus: 'series'
      },
      data: [150, 232, 201, 154, 190, 330]
    },
    {
      name: '产品D',
      type: 'bar',
      stack: 'total',
      emphasis: {
        focus: 'series'
      },
      data: [98, 77, 101, 99, 40, 120]
    }
  ]
})

// 百分比堆积柱形图配置
const percentStackedBarOptions = ref({
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  yAxis: {
    type: 'value',
    name: '占比(%)',
    axisLabel: {
      formatter: '{value}%'
    }
  },
  series: [
    {
      name: '产品A',
      type: 'bar',
      stack: 'total',
      emphasis: {
        focus: 'series'
      },
      data: [20, 25, 18, 22, 15, 28]
    },
    {
      name: '产品B',
      type: 'bar',
      stack: 'total',
      emphasis: {
        focus: 'series'
      },
      data: [35, 30, 38, 40, 45, 35]
    },
    {
      name: '产品C',
      type: 'bar',
      stack: 'total',
      emphasis: {
        focus: 'series'
      },
      data: [25, 28, 25, 23, 25, 22]
    },
    {
      name: '产品D',
      type: 'bar',
      stack: 'total',
      emphasis: {
        focus: 'series'
      },
      data: [20, 17, 19, 15, 15, 15]
    }
  ]
})

// 堆叠折线图配置
const stackedLineOptions = ref({
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
  },
  yAxis: {
    type: 'value',
    name: '销量(台)'
  },
  series: [
    {
      name: '线上渠道',
      type: 'line',
      stack: 'Total',
      smooth: true,
      emphasis: {
        focus: 'series'
      },
      data: [120, 132, 101, 134, 90, 230, 210]
    },
    {
      name: '线下门店',
      type: 'line',
      stack: 'Total',
      smooth: true,
      emphasis: {
        focus: 'series'
      },
      data: [220, 182, 191, 234, 290, 330, 310]
    },
    {
      name: '代理商',
      type: 'line',
      stack: 'Total',
      smooth: true,
      emphasis: {
        focus: 'series'
      },
      data: [150, 232, 201, 154, 190, 330, 410]
    },
    {
      name: '直销',
      type: 'line',
      stack: 'Total',
      smooth: true,
      emphasis: {
        focus: 'series'
      },
      data: [98, 77, 101, 99, 40, 120, 180]
    }
  ]
})

// 自定义 tooltip 配置测试
const customTooltipOptions = ref({
  xAxis: {
    type: 'category',
    data: ['产品A', '产品B', '产品C', '产品D', '产品E']
  },
  yAxis: {
    type: 'value',
    name: '销量(台)'
  },
  tooltip: {
    trigger: 'axis',
    formatter: function(params) {
      let result = `<div style="padding: 8px; background: #fff; border: 1px solid #ccc; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">`
      result += `<div style="font-weight: bold; margin-bottom: 6px; color: #333;">${params[0].name}</div>`

      params.forEach(param => {
        result += `<div style="margin: 4px 0; display: flex; align-items: center;">`
        result += `<span style="display: inline-block; width: 10px; height: 10px; background: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>`
        result += `<span style="color: #666; margin-right: 8px;">${param.seriesName}:</span>`
        result += `<span style="font-weight: bold; color: #333;">${param.value}台</span>`
        result += `</div>`
      })

      result += `</div>`
      return result
    }
  },
  series: [
    {
      name: '销量',
      type: 'bar',
      data: [120, 200, 150, 80, 110],
      itemStyle: {
        color: '#409EFF'
      }
    }
  ]
})
</script>

<style scoped>
.demo-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.demo-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.demo-section h2 {
  margin-top: 0;
  color: #303133;
  border-bottom: 2px solid #409EFF;
  padding-bottom: 10px;
  font-size: 18px;
}

.custom-title {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: linear-gradient(90deg, #409EFF, #67C23A);
  color: white;
  border-radius: 6px;
  font-weight: bold;
  flex-wrap: wrap;
}

.el-button {
  margin-top: 10px;
}

/* 平板设备优化 */
@media (max-width: 1024px) {
  .demo-container {
    padding: 16px;
  }

  .demo-section {
    padding: 16px;
    margin-bottom: 30px;
  }

  .demo-section h2 {
    font-size: 16px;
  }
}

/* 手机设备优化 */
@media (max-width: 768px) {
  .demo-container {
    padding: 12px;
    margin: 0;
    max-width: 100%;
  }

  .demo-section {
    padding: 12px;
    margin-bottom: 20px;
    border-radius: 4px;
    border-left: none;
    border-right: none;
  }

  .demo-section h2 {
    font-size: 16px;
    padding-bottom: 8px;
    margin-bottom: 12px;
  }

  .custom-title {
    padding: 8px 12px;
    font-size: 14px;
    gap: 6px;
  }
}

/* 小屏手机优化 */
@media (max-width: 480px) {
  .demo-container {
    padding: 8px;
  }

  .demo-section {
    padding: 8px;
    margin-bottom: 16px;
    border: none;
    border-top: 1px solid #e4e7ed;
    border-bottom: 1px solid #e4e7ed;
    border-radius: 0;
  }

  .demo-section h2 {
    font-size: 14px;
    padding-bottom: 6px;
    margin-bottom: 10px;
  }

  .custom-title {
    padding: 6px 10px;
    font-size: 12px;
    gap: 4px;
  }

  .el-button {
    width: 100%;
    margin-top: 8px;
  }
}

/* 横屏手机优化 */
@media (max-width: 896px) and (orientation: landscape) {
  .demo-container {
    padding: 8px 16px;
  }

  .demo-section {
    margin-bottom: 16px;
  }
}

/* 调试样式 - 显示图表容器边界 */
.demo-section {
  position: relative;
}

.demo-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px dashed rgba(255, 0, 0, 0.3);
  pointer-events: none;
  z-index: 1000;
}

/* 移动端调试信息 */
@media (max-width: 768px) {
  .demo-section p {
    background: rgba(0, 255, 0, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
  }
}
</style>
