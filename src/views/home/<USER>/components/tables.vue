<template>
  <div class="flex-box">
    <el-card class="flex-item" style="margin-bottom: 12px">
      <div class="ratio-width" style="padding-bottom: 57%">
        <div ref="target" class="ratio-width__wrap">
          <el-table
            stripe
            :data="props.tableA"
            class="table-box"
            height="100%"
            style="width: 100%"
            :border="true"
            :fit="true"
          >
            <!-- 查询类型0发动机，1整车 -->
            <el-table-column
              label="排行"
              type="index"
              width="56"
              align="left"
              header-align="center"
            />
            <el-table-column
              v-if="props.queryType === '0'"
              prop="engineFactory"
              show-overflow-tooltip
              min-width="70"
              align="left"
              header-align="center"
            >
              <template #header>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  content="发动机企业"
                  placement="top-start"
                >
                  <div>发动机企业</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column
              v-if="props.queryType === '1'"
              prop="company"
              show-overflow-tooltip
              align="right"
              header-align="center"
            >
              <template #header>
                <el-tooltip class="box-item" effect="dark" content="企业" placement="top-start">
                  <div>企业</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column
              prop="sales"
              min-width="60"
              show-overflow-tooltip
              align="right"
              header-align="center"
            >
              <template #header>
                <el-tooltip class="box-item" effect="dark" content="销量(台)" placement="top-start">
                  <div>销量(台)</div>
                </el-tooltip>
              </template>
              <template #default="{ row }">
                {{ numFormat(row.sales, 0) }}
              </template>
            </el-table-column>
            <el-table-column
              min-width="55"
              show-overflow-tooltip
              align="right"
              header-align="center"
            >
              <template #header>
                <el-tooltip class="box-item" effect="dark" content="同比" placement="top-start">
                  <div>同比</div>
                </el-tooltip>
              </template>
              <template #default="{ row }">
                <span
                  :style="{
                    color: row.sales_prop ? (row.sales_prop.includes('-') ? '#f00' : '') : ''
                  }"
                  >{{ row.sales_prop ? numFormat(row.sales_prop) : '/' }}</span
                >
              </template>
            </el-table-column>
            <el-table-column
              min-width="55"
              show-overflow-tooltip
              align="right"
              header-align="center"
            >
              <template #header>
                <el-tooltip class="box-item" effect="dark" content="占有率" placement="top-start">
                  <div>占有率</div>
                </el-tooltip>
              </template>
              <template #default="{ row }">
                <span
                  :style="{
                    color: row.proportion ? (row.proportion.includes('-') ? '#f00' : '') : ''
                  }"
                  >{{ row.proportion ? numFormat(row.proportion) : '/' }}</span
                >
              </template>
            </el-table-column>
            <el-table-column
              min-width="70"
              show-overflow-tooltip
              align="right"
              header-align="center"
            >
              <template #header>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  content="占有率同比"
                  placement="top-start"
                >
                  <div>占有率同比</div>
                </el-tooltip>
              </template>
              <template #default="{ row }">
                <span
                  :style="{
                    color: row.prop_change ? (row.prop_change.includes('-') ? '#f00' : '') : ''
                  }"
                  >{{ row.prop_change ? numFormat(row.prop_change) : '/' }}</span
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>
    <el-card class="flex-item">
      <div class="ratio-width" style="padding-bottom: 57%">
        <div ref="target" class="ratio-width__wrap">
          <!--  -->

          <el-table
            stripe
            :key="time"
            :data="props.tableB"
            class="table-box"
            height="100%"
            style="width: 100%"
            :border="true"
            :row-style="rowStyle"
          >
            <!--  -->

            <!-- 查询类型0发动机，1整车 -->
            <!-- <el-table-column label="排行" type="index" width="56" align="center" /> -->
            <el-table-column
              v-if="props.queryType === '0'"
              prop="market"
              show-overflow-tooltip
              min-width="70"
              align="left"
              header-align="center"
            >
              <template #header>
                <el-tooltip class="box-item" effect="dark" content="细分市场" placement="top-start">
                  <div>细分市场</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column
              v-if="props.queryType === '1'"
              show-overflow-tooltip
              align="center"
              header-align="center"
            >
              <template #header>
                <el-tooltip class="box-item" effect="dark" content="区域" placement="top-start">
                  <div>区域</div>
                </el-tooltip>
              </template>
              <template #default="{ row }">
                <div class="area-item" @click="toggleAreaItem(row.area)">{{ row.area }}</div>
              </template>
            </el-table-column>

            <el-table-column
              prop="sales"
              min-width="60"
              show-overflow-tooltip
              align="right"
              header-align="center"
            >
              <template #header>
                <el-tooltip class="box-item" effect="dark" content="销量(台)" placement="top-start">
                  <div>销量(台)</div>
                </el-tooltip>
              </template>
              <template #default="{ row }">
                {{ numFormat(row.sales, 0) }}
              </template>
            </el-table-column>
            <el-table-column
              min-width="55"
              show-overflow-tooltip
              align="right"
              header-align="center"
            >
              <template #header>
                <el-tooltip class="box-item" effect="dark" content="同比" placement="top-start">
                  <div>同比</div>
                </el-tooltip>
              </template>
              <template #default="{ row }">
                <span
                  :style="{
                    color: row.sales_prop ? (row.sales_prop.includes('-') ? '#f00' : '') : ''
                  }"
                  >{{ row.sales_prop ? numFormat(row.sales_prop) : '/' }}</span
                >
              </template>
            </el-table-column>
            <el-table-column
              min-width="55"
              show-overflow-tooltip
              align="right"
              header-align="center"
            >
              <template #header>
                <el-tooltip class="box-item" effect="dark" content="占有率" placement="top-start">
                  <div>占有率</div>
                </el-tooltip>
              </template>
              <template #default="{ row }">
                <span
                  :style="{
                    color: row.proportion ? (row.proportion.includes('-') ? '#f00' : '') : ''
                  }"
                  >{{ row.proportion ? numFormat(row.proportion) : '/' }}</span
                >
              </template>
            </el-table-column>
            <el-table-column
              min-width="70"
              show-overflow-tooltip
              align="right"
              header-align="center"
            >
              <template #header>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  content="占有率同比"
                  placement="top-start"
                >
                  <div>占有率同比</div>
                </el-tooltip>
              </template>
              <template #default="{ row }">
                <span
                  :style="{
                    color: row.prop_change ? (row.prop_change.includes('-') ? '#f00' : '') : ''
                  }"
                  >{{ row.prop_change ? numFormat(row.prop_change) : '/' }}</span
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { watch } from 'vue'
import { numFormat } from '../../../components/echarts/config'

const emits = defineEmits(['change', 'changeArea'])
const props = defineProps({
  tableA: {
    type: Array,
    required: true,
    default: () => []
  },
  tableB: {
    type: Array,
    required: true,
    default: () => []
  },
  queryType: {
    // 查询类型0发动机，1整车
    type: String,
    required: true,
    default: '0'
  },
  params: {
    type: Object,
    required: true,
    default: () => ({})
  },
  currentDrillLevel: {
    type: Number,
    required: true,
    default: 1
  }
})
const params = props.params
const time = ref(Date.now())

watch(
  () => props.tableA,
  () => {
    
    if (props.queryType === '1') {
      time.value = Date.now()
    }
  },
  {
    deep: true,    // 深度监听数组内容变化
    // immediate: true // 立即执行一次
  }
)
const toggleAreaItem = ev => {
  if (props.currentDrillLevel === 2 || props.currentDrillLevel === 3 ) {
    time.value = Date.now()
    emits('change', ev)
    
  }
}
const rowStyle = ({ row }) => {
  return {
    'font-weight': row.area == params.city ? 'bold' : 'normal',
    'font-style': row.area == params.city ? 'italic' : ''
  }
}
</script>

<style lang="scss" scoped>
.flex-box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.flex-item {
  flex: 1;
}
.area-item {
  min-width: 100%;
  cursor: pointer;
}
</style>
